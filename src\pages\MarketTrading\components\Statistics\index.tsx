import CustomButton from '@/components/CustomButton';
import CustomDateV2 from '@/components/CustomDateV2';
import { AGetStatistic, AUpdateCalProfit } from '@/services/longTrading';
import { useModel, useRequest } from '@umijs/max';
import dayjs from 'dayjs';
import React, { useContext, useEffect, useRef } from 'react';
import { MyContext } from '../../context';
import styles from './index.sass';

interface Props {
  name?: string;
}

const Index: React.FC<Props> = (props: Props) => {
  const { setLoad } = useModel('market');
  const {} = props;
  const container = useRef<HTMLDivElement>(null);

  const { timeType, setTimeType, time, setTime } = useContext(MyContext);
  const { data, run } = useRequest(AGetStatistic, {
    defaultParams: [{ timeStr: time.format('YYYY-MM-DD') }],
  });

  const disabledDate = (current: dayjs.Dayjs) => {
    if (timeType === '当日') {
      return current && current > dayjs().endOf('day');
    } else if (timeType === '当月') {
      return current && current > dayjs().endOf('month');
    } else if (timeType === '当年') {
      return current && current > dayjs().endOf('year');
    }
    return false;
  };

  useEffect(() => {
    let timeStr = time.format('YYYY-MM-DD');
    if (timeType === '当月') {
      timeStr = time.format('YYYY-MM');
    } else if (timeType === '当年') {
      timeStr = time.format('YYYY');
    }
    run({ timeStr });
  }, [timeType]);

  const getActiveClass = (type: string) => {
    if (type === timeType) return styles['active'];
  };

  const d = data?.statisticsVo;
  if (!d) {
    return '';
  }

  let picker = 'date';
  if (timeType === '当月') {
    picker = 'month';
  } else if (timeType === '当年') {
    picker = 'year';
  }

  const onTimeChange = (t, ts) => {
    setTime(t);
    run({ timeStr: ts });
  };

  // 按钮点击处理
  const handleButtonClick = () => {
    let dayStr = time.format('YYYY-MM-DD');
    if (timeType === '当月') {
      dayStr = time.format('YYYY-MM');
    } else if (timeType === '当年') {
      dayStr = time.format('YYYY');
    }
    setLoad(true);
    AUpdateCalProfit({ dayStr }).then(() => {
      setTimeout(() => {
        setLoad(false);
      }, 3000);
    });
  };

  return (
    <div className={styles.box} ref={container}>
      <div className={styles.filter}>
        <div className={styles.tabs}>
          <div
            className={getActiveClass('当日')}
            onClick={() => {
              setTimeType('当日');
            }}
          >
            日度
          </div>
          <div
            className={getActiveClass('当月')}
            onClick={() => {
              setTimeType('当月');
            }}
          >
            月度
          </div>
          <div
            className={getActiveClass('当年')}
            onClick={() => {
              setTimeType('当年');
            }}
          >
            年度
          </div>
        </div>
        {/* <ConfigProvider
          theme={{
            components: {
              DatePicker: {
                fontSize: px(18),
                controlHeight: px(35),
                colorText: '#fff',
                colorBgContainer: 'rgba(0,0,0,0)',
                colorTextPlaceholder: '#fff',
                colorBgElevated: 'rgba(1, 17, 61, 0.9)',
                colorTextHeading: '#fff',
                colorIcon: '#fff',
              },
            },
          }}
        >
          <DatePicker
            value={time}
            className={styles.date}
            picker={picker}
            allowClear={false}
            onChange={onTimeChange}
          ></DatePicker>
        </ConfigProvider> */}
        <div className={styles.dateContainer}>
          <CustomDateV2
            value={time}
            allowClear={false}
            className={styles.date}
            picker={picker}
            onChange={onTimeChange}
            disabledDate={disabledDate}
          ></CustomDateV2>
          <CustomButton className={styles.btn} onClick={handleButtonClick}>
            重新计算
          </CustomButton>
        </div>
      </div>

      <div className={styles.container}>
        <div className={styles.title}>累计参与调节次数</div>
        <div className={styles.value}>{d.count}次</div>
      </div>
      <div className={styles.container}>
        <div className={styles.title}>累计参与调节时长</div>
        <div className={styles.value}>{d.hourCount}小时</div>
      </div>
      <div className={styles.container}>
        <div className={styles.title}>总调节电量</div>
        <div className={styles.value}>
          {d.volume}
          {data?.volumeUnit}
        </div>
      </div>
      <div className={styles.container}>
        <div className={styles.title}>总调节收益</div>
        <div className={styles.value}>
          {d.adjustprofit}
          {data?.profitUnit}
        </div>
      </div>
      <div className={styles.container}>
        <div className={styles.title}>运营商收益</div>
        <div className={styles.value}>
          {d.vppProfit}
          {data?.profitUnit}
        </div>
      </div>
    </div>
  );
};

export default Index;
