@import '@/assets/css/helpers.sass'
.leftMenu, .rightMenu
  position: fixed
  top: 53%
  transform: translateY(-50%)
  display: flex
  flex-direction: column
  gap: px(70)
  padding: px(16)

.leftMenu
  left: px(15)

.rightMenu
  right: px(40)

.menuItem
  background-color: rgba(6, 48, 109, 0.8)
  color: white
  padding: px(15) px(25)
  border-radius: px(5)
  cursor: pointer
  text-align: center
  font-size: px(16)
  min-width: px(120)
  transition: all 0.3s ease
  position: relative

  &:hover
    background-color: rgba(32, 219, 253, 0.8)
    transform: scale(1.05)

.menuWrapper
  position: relative

.subMenu
  position: absolute
  top: 100%
  left: px(25)
  width: px(180)
  max-height: px(300)
  background: rgba(2, 22, 41, 0.8)
  border-radius: px(4)
  padding: px(10)
  z-index: 1000
  overflow-y: auto

  // 自定义滚动条样式
  &::-webkit-scrollbar
    width: px(6)

  &::-webkit-scrollbar-track
    background: rgba(255, 255, 255, 0.1)
    border-radius: px(3)

  &::-webkit-scrollbar-thumb
    background: rgba(0, 225, 255, 0.6)
    border-radius: px(3)

    &:hover
      background: rgba(0, 225, 255, 0.8)

.subMenuItem
  display: flex
  justify-content: center
  align-items: center
  padding: px(10) px(16)
  cursor: pointer
  font-size: px(20)
  color: #00E1FF
  font-family: youshe
  transition: all 0.3s ease

  &:hover
    transform: scale(1.05)
