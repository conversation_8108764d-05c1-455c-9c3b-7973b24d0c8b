import CustomTab from '@/components/CustomTab';
import React from 'react';
import ResourceTable from './components/ResourceTable';
import TradeTable from './components/TradeTable';
import UserTable from './components/UserTable';

import { useModel } from '@umijs/max';
import { TabsProps } from 'antd';
import DateTable from './components/DateTable';
import IncomeTable from './components/IncomeTable';
import MedLongTable from './components/MedLongTable';
import SumTable from './components/SumTable';

import ResourceTypeBenefit from './components/ResourceTypeBenefit';
import UserRank from './components/UserRank';
import styles from './index.sass';

interface Props {
  name: string;
}

const EfficientControl: React.FC<Props> = () => {
  const { unitType } = useModel('unitType');
  const items: TabsProps['items'] = [
    {
      key: '1',
      label: '每日收益结算信息',
      children: <TradeTable></TradeTable>,
    },
    {
      key: '2',
      label: '各用户收益结算信息',
      children: <UserTable></UserTable>,
    },
    {
      key: '3',
      label: '各资源点收益结算信息',
      children: <ResourceTable></ResourceTable>,
    },
    ...(unitType === '#2F'
      ? [
          {
            key: '4',
            label: '日期表',
            children: <DateTable></DateTable>,
          },
          {
            key: '5',
            label: '售电收入',
            children: <IncomeTable></IncomeTable>,
          },
          {
            key: '6',
            label: '中长期表',
            children: <MedLongTable></MedLongTable>,
          },
          {
            key: '7',
            label: '总统计表',
            children: <SumTable></SumTable>,
          },
        ]
      : []),
  ].filter(Boolean);

  return (
    <div className={styles.box}>
      <div className={styles.left}>
        <div className={styles.title}>{unitType} 收益结算信息</div>
        <CustomTab className={styles.tab} defaultActiveKey="5" items={items} />
      </div>
      <div className={styles.right}>
        <div className={styles.detail}>
          {/* <DetailChart></DetailChart>
          <Statistic></Statistic> */}
          <UserRank></UserRank>
        </div>
        <div className={styles.state}>
          <ResourceTypeBenefit></ResourceTypeBenefit>
        </div>
      </div>
    </div>
  );
};

export default EfficientControl;
