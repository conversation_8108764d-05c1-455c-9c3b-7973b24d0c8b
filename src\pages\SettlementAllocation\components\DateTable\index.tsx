import CustomButton from '@/components/CustomButton';
import CustomDate from '@/components/CustomDate';
import CustomTableV2 from '@/components/CustomTableV2';
import { ADownLoadExcel, AGetDateTable } from '@/services/settlementAllocation';
import px from '@/utils/px';
import { useModel } from '@umijs/max';
import { useAntdTable } from 'ahooks';
import { Form, message } from 'antd';
import Table, { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import React, { useEffect, useRef } from 'react';
import { IResourceBenefitData } from '../../typing';
import styles from './index.sass';

interface Props {
  name?: string;
}
const Index: React.FC<Props> = (props: Props) => {
  const {} = props;
  const { unitType } = useModel('unitType');
  const container = useRef<HTMLDivElement>(null);

  const [form] = Form.useForm();
  const getTableData = (
    params: { pageSize: number; current: number },
    formData?: any,
  ) => {
    const { current } = params;
    const { dateStr } = formData || {};
    return AGetDateTable({
      dateStr: dateStr.format('YYYY-MM-DD'),
      pageSize: 100,
      current,
    });
  };

  const { tableProps, search } = useAntdTable(getTableData, {
    form,
    defaultParams: [{ current: 1, pageSize: 20 }],
    defaultType: 'advance',
    refreshDeps: [unitType],
  });

  const { submit, reset } = search;

  useEffect(() => {}, [unitType]);

  const columns: ColumnsType<IResourceBenefitData> = [
    {
      title: '时段',
      dataIndex: 'period',
      key: 'period',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '政府授权',
      children: [
        {
          title: '政府授权电量(MWh)',
          dataIndex: 'gaVol',
          key: 'gaVol',
          align: 'center',
        },
        {
          title: '政府授权价格(元/MWh)',
          dataIndex: 'gaPrice',
          key: 'gaPrice',
          align: 'center',
        },
        {
          title: '政府授权电费(元)',
          dataIndex: 'gaBills',
          key: 'gaBills',
          align: 'center',
        },
      ],
    },
    {
      title: '银东双边',
      children: [
        {
          title: '银东双边电量(MWh)',
          dataIndex: 'bilateralVol',
          key: 'bilateralVol',
          align: 'center',
        },
        {
          title: '银东双边价格(元/MWh)',
          dataIndex: 'bilateralPrice',
          key: 'bilateralPrice',
          align: 'center',
        },
        {
          title: '银东双边电费(元)',
          dataIndex: 'bilateralBills',
          key: 'bilateralBills',
          align: 'center',
        },
      ],
    },
    {
      title: '银东竞价',
      children: [
        {
          title: '银东竞价电量(MWh)',
          dataIndex: 'biddingVol',
          key: 'biddingVol',
          align: 'center',
        },
        {
          title: '银东竞价价格(元/MWh)',
          dataIndex: 'biddingPrice',
          key: 'biddingPrice',
          align: 'center',
        },
        {
          title: '银东竞价电费(元)',
          dataIndex: 'biddingBills',
          key: 'biddingBills',
          align: 'center',
        },
      ],
    },
    {
      title: '省内中长期',
      children: [
        {
          title: '合约电量(MWh)',
          dataIndex: 'contractVol',
          key: 'contractVol',
          align: 'center',
        },
        {
          title: '合约价格(元/MWh)',
          dataIndex: 'contractPrice',
          key: 'contractPrice',
          align: 'center',
        },
        {
          title: '合约电费(元)',
          dataIndex: 'contractBills',
          key: 'contractBills',
          align: 'center',
        },
      ],
    },
    {
      title: '日前交易',
      children: [
        {
          title: '日前出清电量(MWh)',
          dataIndex: 'dayAheadClearVol',
          key: 'dayAheadClearVol',
          align: 'center',
        },
        {
          title: '日前偏差结算电量(MWh)',
          dataIndex: 'dayAheadBiasVol',
          key: 'dayAheadBiasVol',
          align: 'center',
        },
        {
          title: '日前统一结算价格(元/MWh)',
          dataIndex: 'dayAheadPrice',
          key: 'dayAheadPrice',
          align: 'center',
        },
        {
          title: '日前偏差电费(元)',
          dataIndex: 'dayAheadBills',
          key: 'dayAheadBills',
          align: 'center',
        },
      ],
    },
    {
      title: '实时交易',
      children: [
        {
          title: '实时用电量(MWh)',
          dataIndex: 'realTimeVol',
          key: 'realTimeVol',
          align: 'center',
        },
        {
          title: '实时偏差结算电量(MWh)',
          dataIndex: 'realTimeBiasVol',
          key: 'realTimeBiasVol',
          align: 'center',
        },
        {
          title: '实时统一结算价格(元/MWh)',
          dataIndex: 'realTimePrice',
          key: 'realTimePrice',
          align: 'center',
        },
        {
          title: '实时偏差电费(元)',
          dataIndex: 'realTimeBills',
          key: 'realTimeBills',
          align: 'center',
        },
      ],
    },
    {
      title: '其他系统运行费用',
      children: [
        {
          title: '调频机会成本分摊(元)',
          dataIndex: 'frequencyCost',
          key: 'frequencyCost',
          align: 'center',
        },
      ],
    },
    {
      title: '运行成本费用',
      children: [
        {
          title: '启动分摊(元)',
          dataIndex: 'startAllocation',
          key: 'startAllocation',
          align: 'center',
        },
        {
          title: '特殊机组补偿分摊(元)',
          dataIndex: 'compensationAllocation',
          key: 'compensationAllocation',
          align: 'center',
        },
      ],
    },
    {
      title: '市场偏差类费用',
      children: [
        {
          title: '市场超额收益返还(元)',
          dataIndex: 'excessMarketReturns',
          key: 'excessMarketReturns',
          align: 'center',
        },
        {
          title: '用户侧日前申报偏差收益回收(元)',
          dataIndex: 'gainsRecovery',
          key: 'gainsRecovery',
          align: 'center',
        },
        {
          title: '用户侧日前申报偏差收益返还(元)',
          dataIndex: 'gainsReturn',
          key: 'gainsReturn',
          align: 'center',
        },
      ],
    },
    {
      title: '集中调用时段价差费用',
      children: [
        {
          title: '集中调用时段价差费用(元)',
          dataIndex: 'priceDiffExpense',
          key: 'priceDiffExpense',
          align: 'center',
          width: px(200),
        },
      ],
    },
    {
      title: '总计',
      children: [
        {
          title: '总结算电量(MWh)',
          dataIndex: 'sumVol',
          key: 'sumVol',
          align: 'center',
        },
        {
          title: '总结算电价(元/MWh)',
          dataIndex: 'sumPrice',
          key: 'sumPrice',
          align: 'center',
        },
        {
          title: '总结算电费(元)',
          dataIndex: 'sumBills',
          key: 'sumBills',
          align: 'center',
        },
      ],
    },
  ];

  return (
    <>
      <div className={styles.box} ref={container}>
        <Form
          className={styles.form}
          form={form}
          initialValues={{
            dateStr: dayjs('2025-07-13'),
          }}
        >
          <Form.Item name="dateStr" label="选择日期">
            <CustomDate allowClear={false} className={styles.date}></CustomDate>
          </Form.Item>
          <Form.Item>
            <CustomButton className={styles.button} onClick={submit}>
              筛选
            </CustomButton>
          </Form.Item>
          <Form.Item>
            <CustomButton className={styles.button} onClick={reset}>
              重置
            </CustomButton>
          </Form.Item>
          <Form.Item>
            <CustomButton
              className={styles.button}
              onClick={() => {
                ADownLoadExcel({ type: '资源收益' }).then(() => {
                  message.success('下载成功！');
                });
              }}
            >
              下载
            </CustomButton>
          </Form.Item>
        </Form>
        <div className={styles.table}>
          <CustomTableV2
            {...tableProps}
            bordered
            columns={columns}
            scroll={{ x: px(5000), y: px(520) }}
            pagination={false}
            summary={(pagedata) => {
              console.log(pagedata);
              return (
                <Table.Summary fixed>
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0}>当日总计</Table.Summary.Cell>
                    <Table.Summary.Cell index={1}></Table.Summary.Cell>
                  </Table.Summary.Row>
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0}>当月累计</Table.Summary.Cell>
                    <Table.Summary.Cell index={1}></Table.Summary.Cell>
                  </Table.Summary.Row>
                </Table.Summary>
              );
            }}
          ></CustomTableV2>
        </div>
      </div>
    </>
  );
};

export default Index;
